"""
인스타그램 자동화 설정 파일
"""
import os
from dotenv import load_dotenv

# .env 파일에서 환경 변수 로드
load_dotenv()

# 인스타그램 계정 정보
USERNAME = os.getenv('INSTAGRAM_USERNAME', '')
PASSWORD = os.getenv('INSTAGRAM_PASSWORD', '')

# Gemini API 설정
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')

# 자동화 설정
HASHTAGS = ['여행', '맛집', '일상', '데일리']  # 검색할 해시태그 목록
LIKE_PER_HASHTAG = 10  # 각 해시태그당 좋아요 수
COMMENTS = [
    '멋진 게시물이네요!',
    '정말 좋은 사진이에요!',
    '잘 보고 갑니다~',
    '와우! 대단해요!',
    '좋은 하루 되세요!',
    '정말 인상적인 게시물입니다.',
    '잘 봤습니다. 감사합니다.',
    '멋진 컨텐츠 감사합니다.'
]  # 기본 댓글 목록 (AI 생성 실패 시 사용)
FOLLOW_RATIO = 0.3  # 좋아요 누른 게시물 중 팔로우할 비율

# 게시물 설정
POST_FREQUENCY = 1  # 하루에 게시할 게시물 수
POST_TOPICS = ['여행', '음식', '패션', '일상']  # 게시물 주제

# 브라우저 설정
HEADLESS = False  # 헤드리스 모드 (True: 브라우저 창 숨김)
IMPLICIT_WAIT = 10  # 암시적 대기 시간 (초)

# AI 설정
USE_AI_COMMENTS = True  # AI로 댓글 생성 여부
USE_AI_CAPTIONS = True  # AI로 캡션 생성 여부
COMMENT_STYLE = "친근하고 긍정적인"  # 댓글 스타일
CAPTION_STYLE = "감성적이고 창의적인"  # 캡션 스타일
